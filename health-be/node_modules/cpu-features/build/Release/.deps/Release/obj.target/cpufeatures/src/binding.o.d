cmd_Release/obj.target/cpufeatures/src/binding.o := g++ -o Release/obj.target/cpufeatures/src/binding.o ../src/binding.cc '-DNODE_GYP_MODULE_NAME=cpufeatures' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DCPU_FEATURES_VERSION_REV=8a494eb1e158ec2050e5f699a504fbc9b896a43b' '-DBUILDING_NODE_EXTENSION' -I/home/<USER>/.cache/node-gyp/22.15.0/include/node -I/home/<USER>/.cache/node-gyp/22.15.0/src -I/home/<USER>/.cache/node-gyp/22.15.0/deps/openssl/config -I/home/<USER>/.cache/node-gyp/22.15.0/deps/openssl/openssl/include -I/home/<USER>/.cache/node-gyp/22.15.0/deps/uv/include -I/home/<USER>/.cache/node-gyp/22.15.0/deps/zlib -I/home/<USER>/.cache/node-gyp/22.15.0/deps/v8/include -I../src -I../../nan -I../deps/cpu_features/include  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -O3 -m64 -O3 -fno-omit-frame-pointer -fno-rtti -fno-exceptions -fno-strict-aliasing -std=gnu++17 -MMD -MF ./Release/.deps/Release/obj.target/cpufeatures/src/binding.o.d.raw   -c
Release/obj.target/cpufeatures/src/binding.o: ../src/binding.cc \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/cppgc/common.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8config.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-array-buffer.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-local-handle.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-handle-base.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-internal.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8config.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-object.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-maybe.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-persistent-handle.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-weak-callback-info.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-primitive.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-data.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-value.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-traced-handle.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-container.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-context.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-snapshot.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-isolate.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-callbacks.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-promise.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-debug.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-script.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-memory-span.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-message.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-embedder-heap.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-function-callback.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-microtask.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-statistics.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-unwinder.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-embedder-state-scope.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-date.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-exception.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-extension.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-external.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-function.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-template.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-initialization.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-platform.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-source-location.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-json.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-locker.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-microtask-queue.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-primitive-object.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-proxy.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-regexp.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-typed-array.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-value-serializer.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-version.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-wasm.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node_version.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node_api.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/js_native_api.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/js_native_api_types.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node_api_types.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node_buffer.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node.h ../../nan/nan.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node_version.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/uv.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/errno.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/version.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/unix.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/threadpool.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/linux.h \
 /home/<USER>/.cache/node-gyp/22.15.0/include/node/node_object_wrap.h \
 ../../nan/nan_callbacks.h ../../nan/nan_callbacks_12_inl.h \
 ../../nan/nan_maybe_43_inl.h ../../nan/nan_converters.h \
 ../../nan/nan_converters_43_inl.h ../../nan/nan_new.h \
 ../../nan/nan_implementation_12_inl.h ../../nan/nan_persistent_12_inl.h \
 ../../nan/nan_weak.h ../../nan/nan_object_wrap.h ../../nan/nan_private.h \
 ../../nan/nan_typedarray_contents.h ../../nan/nan_json.h \
 ../../nan/nan_scriptorigin.h \
 ../deps/cpu_features/include/cpu_features_macros.h \
 ../deps/cpu_features/include/cpuinfo_x86.h \
 ../deps/cpu_features/include/cpu_features_cache_info.h \
 ../deps/cpu_features/include/cpu_features_macros.h
../src/binding.cc:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/cppgc/common.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8config.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-array-buffer.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-local-handle.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-handle-base.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-internal.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8config.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-object.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-maybe.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-persistent-handle.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-weak-callback-info.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-primitive.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-data.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-value.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-traced-handle.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-container.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-context.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-snapshot.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-isolate.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-callbacks.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-promise.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-debug.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-script.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-memory-span.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-message.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-embedder-heap.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-function-callback.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-microtask.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-statistics.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-unwinder.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-embedder-state-scope.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-date.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-exception.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-extension.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-external.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-function.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-template.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-initialization.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-platform.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-source-location.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-json.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-locker.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-microtask-queue.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-primitive-object.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-proxy.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-regexp.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-typed-array.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-value-serializer.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-version.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/v8-wasm.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node_version.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node_api.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/js_native_api.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/js_native_api_types.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node_api_types.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node_buffer.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node.h:
../../nan/nan.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node_version.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/uv.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/errno.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/version.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/unix.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/threadpool.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/uv/linux.h:
/home/<USER>/.cache/node-gyp/22.15.0/include/node/node_object_wrap.h:
../../nan/nan_callbacks.h:
../../nan/nan_callbacks_12_inl.h:
../../nan/nan_maybe_43_inl.h:
../../nan/nan_converters.h:
../../nan/nan_converters_43_inl.h:
../../nan/nan_new.h:
../../nan/nan_implementation_12_inl.h:
../../nan/nan_persistent_12_inl.h:
../../nan/nan_weak.h:
../../nan/nan_object_wrap.h:
../../nan/nan_private.h:
../../nan/nan_typedarray_contents.h:
../../nan/nan_json.h:
../../nan/nan_scriptorigin.h:
../deps/cpu_features/include/cpu_features_macros.h:
../deps/cpu_features/include/cpuinfo_x86.h:
../deps/cpu_features/include/cpu_features_cache_info.h:
../deps/cpu_features/include/cpu_features_macros.h:
