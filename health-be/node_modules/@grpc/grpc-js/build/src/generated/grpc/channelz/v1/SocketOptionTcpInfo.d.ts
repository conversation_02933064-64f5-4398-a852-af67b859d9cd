/**
 * For use with SocketOption's additional field.  Tcp info for
 * SOL_TCP and TCP_INFO.
 */
export interface SocketOptionTcpInfo {
    'tcpi_state'?: (number);
    'tcpi_ca_state'?: (number);
    'tcpi_retransmits'?: (number);
    'tcpi_probes'?: (number);
    'tcpi_backoff'?: (number);
    'tcpi_options'?: (number);
    'tcpi_snd_wscale'?: (number);
    'tcpi_rcv_wscale'?: (number);
    'tcpi_rto'?: (number);
    'tcpi_ato'?: (number);
    'tcpi_snd_mss'?: (number);
    'tcpi_rcv_mss'?: (number);
    'tcpi_unacked'?: (number);
    'tcpi_sacked'?: (number);
    'tcpi_lost'?: (number);
    'tcpi_retrans'?: (number);
    'tcpi_fackets'?: (number);
    'tcpi_last_data_sent'?: (number);
    'tcpi_last_ack_sent'?: (number);
    'tcpi_last_data_recv'?: (number);
    'tcpi_last_ack_recv'?: (number);
    'tcpi_pmtu'?: (number);
    'tcpi_rcv_ssthresh'?: (number);
    'tcpi_rtt'?: (number);
    'tcpi_rttvar'?: (number);
    'tcpi_snd_ssthresh'?: (number);
    'tcpi_snd_cwnd'?: (number);
    'tcpi_advmss'?: (number);
    'tcpi_reordering'?: (number);
}
/**
 * For use with SocketOption's additional field.  Tcp info for
 * SOL_TCP and TCP_INFO.
 */
export interface SocketOptionTcpInfo__Output {
    'tcpi_state': (number);
    'tcpi_ca_state': (number);
    'tcpi_retransmits': (number);
    'tcpi_probes': (number);
    'tcpi_backoff': (number);
    'tcpi_options': (number);
    'tcpi_snd_wscale': (number);
    'tcpi_rcv_wscale': (number);
    'tcpi_rto': (number);
    'tcpi_ato': (number);
    'tcpi_snd_mss': (number);
    'tcpi_rcv_mss': (number);
    'tcpi_unacked': (number);
    'tcpi_sacked': (number);
    'tcpi_lost': (number);
    'tcpi_retrans': (number);
    'tcpi_fackets': (number);
    'tcpi_last_data_sent': (number);
    'tcpi_last_ack_sent': (number);
    'tcpi_last_data_recv': (number);
    'tcpi_last_ack_recv': (number);
    'tcpi_pmtu': (number);
    'tcpi_rcv_ssthresh': (number);
    'tcpi_rtt': (number);
    'tcpi_rttvar': (number);
    'tcpi_snd_ssthresh': (number);
    'tcpi_snd_cwnd': (number);
    'tcpi_advmss': (number);
    'tcpi_reordering': (number);
}
