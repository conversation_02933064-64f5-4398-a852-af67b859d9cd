{"name": "@js-sdsl/ordered-map", "version": "4.4.2", "description": "javascript standard data structure library which benchmark against C++ STL", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "author": {"name": "ZLY201", "email": "<EMAIL>", "url": "https://github.com/js-sdsl/js-sdsl"}, "sideEffects": false, "homepage": "https://js-sdsl.org", "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}, "lint-staged": {"*.{js,ts}": ["yarn lint"]}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-typescript": "^9.0.2", "@types/babel__core": "^7.1.19", "@types/chai": "^3.5.2", "@types/delete-empty": "^3.0.2", "@types/glob": "^8.1.0", "@types/gulp": "^4.0.9", "@types/gulp-babel": "^6.1.30", "@types/gulp-filter": "^3.0.34", "@types/gulp-rename": "^2.0.1", "@types/gulp-sourcemaps": "^0.0.35", "@types/gulp-tap": "^1.0.1", "@types/gulp-terser": "^1.2.1", "@types/gulp-uglify": "^3.0.7", "@types/karma": "^6.3.3", "@types/merge-stream": "^1.1.2", "@types/mocha": "^9.1.1", "@types/node": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "all-contributors-cli": "^6.20.0", "babel-plugin-remove-unused-import": "^2.1.1", "browserslist": "^4.21.3", "chai": "^3.5.0", "commitlint": "^17.0.3", "compare-versions": "^5.0.1", "conventional-changelog-conventionalcommits": "^5.0.0", "delete-empty": "^3.0.0", "eslint": "^8.23.1", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-compat": "^4.0.2", "eslint-plugin-import": "^2.26.0", "get-npm-package-version": "^1.1.1", "gh-pages": "^3.2.3", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-clean": "^0.4.0", "gulp-filter": "^7.0.0", "gulp-rename": "^2.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-tap": "^2.0.0", "gulp-terser": "^2.1.0", "gulp-typescript": "^5.0.0", "gulp-uglify": "^3.0.2", "husky": "^8.0.1", "karma": "^6.4.1", "karma-chrome-launcher": "^3.1.1", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.2", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-requirejs": "^1.1.0", "karma-safarinative-launcher": "^1.1.0", "karma-typescript": "^5.5.3", "lint-staged": "^13.0.3", "merge-stream": "^2.0.0", "mocha": "^9.2.2", "nyc": "^15.1.0", "requirejs": "^2.3.6", "rollup": "^2.79.1", "rollup-plugin-license": "^3.0.0", "rollup-plugin-ts": "^3.0.2", "ts-macros": "^1.3.3", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "ts-transform-paths": "^2.0.3", "tsconfig-paths": "^4.0.0", "tslib": "^2.4.0", "ttypescript": "^1.5.13", "typedoc": "^0.23.10", "typedoc-plugin-missing-exports": "^1.0.0", "typescript": "~4.7.4"}, "repository": {"type": "github", "url": "https://github.com/js-sdsl/js-sdsl.git"}, "license": "MIT", "keywords": ["data", "structure", "data structure", "rbTree", "rbtree", "RBTree", "red black tree", "ordered", "set", "map", "ordered map", "ordered set", "deque", "heap", "priority queue", "link list", "LinkList", "linkedList", "vector", "stack", "queue", "hash", "hash set", "hash map", "c++", "stl"], "bugs": {"email": "<EMAIL>", "url": "https://github.com/js-sdsl/js-sdsl/issues"}, "dependencies": {}}