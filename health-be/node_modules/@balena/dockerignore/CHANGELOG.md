# Change Log

All notable changes to this project will be documented in this file
automatically by Versionist. DO NOT EDIT THIS FILE MANUALLY!
This project adheres to [Semantic Versioning](http://semver.org/).

# v1.0.2
## (2020-05-13)

* Update dependencies and replace yarn with npm [<PERSON>]

# v1.0.1
## (2020-05-02)

* Fix npm publish via balena CI [Paulo <PERSON>]

# v1.0.0
## (2020-04-29)

* Review README.md [<PERSON>]

# v0.0.6
## (2020-04-29)

* Update CI config (add .resinci.yml and appveyor.yml, remove codecov) [<PERSON>]
* Add LICENSE.md and CHANGELOG.md files (setup versionist) [<PERSON>]
* Update package.json (npm package details - fork from zeit/dockerignore) [Paulo <PERSON>]

## 0.0.5 - 2019-04-22

* Last zeit/dockerignore release before fork as balena-io-modules/dockerignore
