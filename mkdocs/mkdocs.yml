# Project information
site_name: MsArkNet Documentation
site_url: https://docs.msarknet.me/
site_description: MsArkNet documentation.
site_author: MsArkNet
copyright: MsArkNet is licensed under the MIT License (MIT), this <a href="https://readthedocs.org/">Readthedocs.org</a> documentation uses <a href="https://www.mkdocs.org/">Mkdocs</a> and the <a href="https://squidfunk.github.io/mkdocs-material">Material theme</a>.

#extra project info and template customisation
extra:
  support:
    gitter: https://discord.gg/u3Ej2BReNn
    MsArkNet_website: https://docs.MsArkNet.com
    issues: https://github.com/MsArkNet/MsArkNet/issues

# Repository
repo_name: MsArkNet/MsArkNet
repo_url: https://github.com/MsArkNet/MsArkNet
edit_uri: https://github.com/MsArkNet/MsArkNet.Docs/tree/master/docs

theme:
    name: material
    custom_dir: docs/custom_theme
    palette:
        primary: teal
        accent: teal
    favicon: images/favicon.ico
    logo: assets/logo.png
    feature:
      tabs: true
nav:
- What is MsArkNet? : index.md

markdown_extensions:
    - toc:
        permalink: 
        toc_depth: 3
    - codehilite
    - markdown_include.include:
        base_path: docs
    - admonition
    - footnotes
    - def_list
    - abbr
    - pymdownx.arithmatex
    - pymdownx.betterem:
          smart_enable: all
    - pymdownx.keys
    - pymdownx.details
    - pymdownx.emoji
    - pymdownx.magiclink
    - pymdownx.mark
    - pymdownx.smartsymbols
    - pymdownx.superfences
    - pymdownx.tasklist:
          custom_checkbox: true
    - pymdownx.tilde
    - meta
    - smarty

plugins:
    - search
